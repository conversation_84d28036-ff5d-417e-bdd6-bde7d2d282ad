from __future__ import annotations
from autogen import ConversableAgent, register_function
import os, sys, re, ast
from typing import Dict, List, get_type_hints

SCORE_KEYWORDS: dict[int, list[str]] = {
    1: ["awful", "horrible", "disgusting"],
    2: ["bad", "unpleasant", "offensive"],
    3: ["average", "uninspiring", "forgettable"],
    4: ["good", "enjoyable", "satisfying"],
    5: ["awesome", "incredible", "amazing"]
}

# ────────────────────────────────────────────────────────────────
# 0. OpenAI API key setup ── *Do **not** modify this block.*
# ────────────────────────────────────────────────────────────────
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "").strip()
if not OPENAI_API_KEY:
    sys.exit("❗ Set the OPENAI_API_KEY environment variable first.")
LLM_CFG = {"config_list": [{"model": "gpt-4o-mini", "api_key": OPENAI_API_KEY}]}

# ────────────────────────────────────────────────────────────────
# 1. Utility data structures & helper functions
# ────────────────────────────────────────────────────────────────

def normalize(text: str) -> str:
    return re.sub(r"\s+", " ", re.sub(r"[^\w\s]", " ", text.lower())).strip()

def fetch_restaurant_data(restaurant_name: str) -> dict[str, list[str]]:
    data = {}
    target = normalize(restaurant_name)
    with open(DATA_PATH, encoding="utf-8") as f:
        for line in f:
            if not line.strip(): continue
            name, review = line.split('.', 1)
            if normalize(name) == target:
                data.setdefault(name.strip(), []).append(review.strip())
    return data


def calculate_overall_score(restaurant_name: str, food_scores: List[int], customer_service_scores: List[int]) -> dict[str, str]:
    """Geometric-mean rating rounded to 3 dp."""
    n = len(food_scores)
    if n == 0 or n != len(customer_service_scores):
        raise ValueError("food_scores and customer_service_scores must be non-empty and same length")
    total = sum(((f**2 * s)**0.5) * (1 / (n * (125**0.5))) * 10 for f, s in zip(food_scores, customer_service_scores))
    return {restaurant_name: f"{total:.3f}"}

# register functions
fetch_restaurant_data.__annotations__ = get_type_hints(fetch_restaurant_data)
calculate_overall_score.__annotations__ = get_type_hints(calculate_overall_score)

# ──────────────────────────────────────────────
# 2. Agent setup
# ──────────────────────────────────────────────

def build_agent(name, msg):
    return ConversableAgent(name=name, system_message=msg, llm_config=LLM_CFG)

DATA_FETCH = build_agent(
    "fetch_agent",
    "You are a data fetch agent. When given a query about a restaurant, extract the restaurant name and call the fetch_restaurant_data function with that name. Be precise with restaurant names."
)
ANALYZER = build_agent(
    "review_analyzer_agent",
    f"You are a review analyzer. Analyze restaurant reviews and extract food and service scores.\n"
    f"For each review, identify ONE food adjective and ONE service adjective, then map them to scores 1-5.\n"
    f"\n"
    f"EXACT KEYWORD MAPPING: {SCORE_KEYWORDS}\n"
    f"\n"
    f"SCORING APPROACH:\n"
    f"1. First, look for exact keyword matches and use their assigned scores\n"
    f"2. For other adjectives, use semantic similarity to map to the closest core keyword\n"
    f"3. When in doubt, be conservative and choose the middle score (3)\n"
    f"\n"
    f"SEMANTIC MAPPING GUIDELINES:\n"
    f"Score 1 (exact: awful, horrible, disgusting): terrible, gross, foul, appalling, dreadful\n"
    f"Score 2 (exact: bad, unpleasant, offensive): poor, disappointing, subpar, mediocre, unsatisfactory\n"
    f"Score 3 (exact: average, uninspiring, forgettable): okay, decent, fair, ordinary, bland\n"
    f"Score 4 (exact: good, enjoyable, satisfying): nice, tasty, great, pleasant, delicious, solid\n"
    f"Score 5 (exact: awesome, incredible, amazing): fantastic, excellent, outstanding, stellar, perfect\n"
    f"\n"
    f"SPECIAL HANDLING:\n"
    f"- For compound phrases, focus on the strongest sentiment word\n"
    f"- For modifiers like 'pretty good' or 'really bad', adjust intensity accordingly\n"
    f"- If no clear adjective exists, infer from overall context\n"
    f"- Always ensure both score lists have exactly the same length\n"
    f"\n"
    f"OUTPUT FORMAT (respond with ONLY this):\n"
    f"food_scores=[score1, score2, score3, ...]\n"
    f"customer_service_scores=[score1, score2, score3, ...]\n"
    f"Both lists must have the same length - one score per review."
)
SCORER = build_agent(
    "scoring_agent",
    "You are a scoring agent. Given a restaurant name and two score lists, call calculate_overall_score function with the restaurant name and both score lists. Use the exact restaurant name provided."
)
ENTRY = build_agent("entry", "Coordinator")

# register functions
register_function(
    fetch_restaurant_data,
    caller=DATA_FETCH,
    executor=ENTRY,
    name="fetch_restaurant_data",
    description="Fetch reviews from specified data file by name.",
)
register_function(
    calculate_overall_score,
    caller=SCORER,
    executor=ENTRY,
    name="calculate_overall_score",
    description="Compute final rating via geometric mean.",
)


# ────────────────────────────────────────────────────────────────
# 3. Conversation helpers
# ────────────────────────────────────────────────────────────────

def run_chat_sequence(entry: ConversableAgent, sequence: list[dict]) -> str:
    ctx = {**getattr(entry, "_initiate_chats_ctx", {})}
    for step in sequence:
        msg = step["message"].format(**ctx)
        chat = entry.initiate_chat(
            step["recipient"], message=msg,
            summary_method=step.get("summary_method", "last_msg"),
            max_turns=step.get("max_turns", 2),
        )
        out = chat.summary

        # Data fetch output - extract restaurant data
        if step["recipient"] is DATA_FETCH:
            for past in reversed(chat.chat_history):
                try:
                    data = ast.literal_eval(past["content"])
                    if isinstance(data, dict) and data and not ("call" in data):
                        restaurant_name = next(iter(data))
                        ctx.update({"reviews_dict": data, "restaurant_name": restaurant_name})
                        break
                except:
                    continue

        # Analyzer output - validate and pass scores
        elif step["recipient"] is ANALYZER:
            # Validate that the analyzer output contains proper score lists
            if "food_scores=" in out and "customer_service_scores=" in out:
                ctx["analyzer_output"] = out
            else:
                # Fallback: try to extract from chat history
                for past in reversed(chat.chat_history):
                    content = past["content"]
                    if "food_scores=" in content and "customer_service_scores=" in content:
                        ctx["analyzer_output"] = content
                        break

        # Scorer output - extract the final numerical score
        elif step["recipient"] is SCORER:
            # Look for the function call result in chat history
            for past in reversed(chat.chat_history):
                try:
                    content = past["content"]
                    if "Response from calling tool" in content:
                        # Try multiple patterns to extract the score
                        import re
                        patterns = [
                            r"'([^']+)':\s*'([0-9.]+)'",  # {'Restaurant': '9.123'}
                            r'"([^"]+)":\s*"([0-9.]+)"',  # {"Restaurant": "9.123"}
                            r":\s*([0-9.]+)",             # : 9.123
                        ]
                        for pattern in patterns:
                            score_match = re.search(pattern, content)
                            if score_match:
                                if len(score_match.groups()) == 2:
                                    score = score_match.group(2)
                                else:
                                    score = score_match.group(1)
                                ctx["final_score"] = score
                                return score
                except:
                    continue

            # Fallback: try to extract from summary
            import re
            score_match = re.search(r"([0-9]+\.?[0-9]*)", out)
            if score_match:
                return score_match.group(1)

    return out

ConversableAgent.initiate_chats = lambda self, seq: run_chat_sequence(self, seq)

# ──────────────────────────────────────────────
# 4. Main entry
# ──────────────────────────────────────────────

def main(user_query: str, data_path: str = "restaurant-data.txt"):
    global DATA_PATH
    DATA_PATH = data_path
    agents = {"data_fetch": DATA_FETCH, "analyzer": ANALYZER, "scorer": SCORER}
    chat_sequence = [
        {"recipient": agents["data_fetch"],
         "message": "Find reviews for this query: {user_query}",
         "summary_method": "last_msg",
         "max_turns": 2},

        {"recipient": agents["analyzer"],
         "message": "Here are the reviews from the data fetch agent:\n{reviews_dict}\n\nAnalyze each review systematically:\n1. Count the total number of reviews first\n2. For each review, identify ONE food adjective and ONE service adjective\n3. Map each adjective to scores 1-5 using the exact keyword mapping or semantic similarity\n4. Double-check that both score lists have the same length as the number of reviews\n\nBe precise with the original keyword mappings: 'uninspiring'=3, 'forgettable'=3, 'unpleasant'=2, 'offensive'=2.",
         "summary_method": "last_msg",
         "max_turns": 3},

        {"recipient": agents["scorer"],
         "message": "Restaurant: {restaurant_name}\n\nScoring data from analyzer:\n{analyzer_output}\n\nPlease call the calculate_overall_score function with the restaurant name and both score lists to compute the final rating.",
         "summary_method": "last_msg",
         "max_turns": 2},
    ]
    ENTRY._initiate_chats_ctx = {"user_query": user_query}
    result = ENTRY.initiate_chats(chat_sequence)
    print(f"result: {result}")
    return result

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print('Usage: python main.py path/to/data.txt "How good is Subway?" ')
        sys.exit(1)

    path = sys.argv[1]
    query = sys.argv[2]
    main(query, path)
