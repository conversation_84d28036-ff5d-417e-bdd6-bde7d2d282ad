from __future__ import annotations
from autogen import ConversableAgent, register_function
import os, sys, re, ast
from typing import Dict, List, get_type_hints

SCORE_KEYWORDS: dict[int, list[str]] = {
    1: ["awful", "horrible", "disgusting"],
    2: ["bad", "unpleasant", "offensive"],
    3: ["average", "uninspiring", "forgettable"],
    4: ["good", "enjoyable", "satisfying"],
    5: ["awesome", "incredible", "amazing"]
}

# ────────────────────────────────────────────────────────────────
# 0. OpenAI API key setup ── *Do **not** modify this block.*
# ────────────────────────────────────────────────────────────────
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "").strip()
if not OPENAI_API_KEY:
    sys.exit("❗ Set the OPENAI_API_KEY environment variable first.")
LLM_CFG = {"config_list": [{"model": "gpt-4o-mini", "api_key": OPENAI_API_KEY}]}

# ────────────────────────────────────────────────────────────────
# 1. Utility data structures & helper functions
# ────────────────────────────────────────────────────────────────

def normalize(text: str) -> str:
    return re.sub(r"\s+", " ", re.sub(r"[^\w\s]", " ", text.lower())).strip()

def fetch_restaurant_data(restaurant_name: str) -> dict[str, list[str]]:
    data = {}
    target = normalize(restaurant_name)
    with open(DATA_PATH, encoding="utf-8") as f:
        for line in f:
            if not line.strip(): continue
            name, review = line.split('.', 1)
            if normalize(name) == target:
                data.setdefault(name.strip(), []).append(review.strip())
    return data


def calculate_overall_score(restaurant_name: str, food_scores: List[int], customer_service_scores: List[int]) -> dict[str, str]:
    """Geometric-mean rating rounded to 3 dp."""
    n = len(food_scores)
    if n == 0 or n != len(customer_service_scores):
        raise ValueError("food_scores and customer_service_scores must be non-empty and same length")
    total = sum(((f**2 * s)**0.5) * (1 / (n * (125**0.5))) * 10 for f, s in zip(food_scores, customer_service_scores))
    return {restaurant_name: f"{total:.3f}"}

# register functions
fetch_restaurant_data.__annotations__ = get_type_hints(fetch_restaurant_data)
calculate_overall_score.__annotations__ = get_type_hints(calculate_overall_score)

# ──────────────────────────────────────────────
# 2. Agent setup
# ──────────────────────────────────────────────

def build_agent(name, msg):
    return ConversableAgent(name=name, system_message=msg, llm_config=LLM_CFG)

DATA_FETCH = build_agent(
    "fetch_agent",
    'Return JSON {"call":"fetch_restaurant_data","args":{"restaurant_name":"<name>"}}'
)
ANALYZER = build_agent(
    "review_analyzer_agent",
    f"You analyze restaurant reviews using a two-step process to extract food and service scores.\n\n"
    f"STEP 1 - KEYWORD EXTRACTION:\n"
    f"For each review, identify and extract exactly one food-related adjective and one service-related adjective.\n"
    f"Each review contains exactly these 2 keywords from the predefined lists.\n\n"
    f"STEP 2 - SCORE MAPPING:\n"
    f"Map each extracted keyword to its numerical score using this mapping:\n"
    f"{SCORE_KEYWORDS}\n\n"
    f"DETAILED ANALYSIS:\n"
    f"Generate a detailed analysis table showing the keyword extraction and scoring process for each review:\n\n"
    f"| Index | Food Keywords | Food Score | Service Keywords | Service Score | Review Text |\n"
    f"|-------|---------------|------------|------------------|---------------|-------------|\n"
    f"| 1     | amazing       | 5.0        | good             | 4.0           | \"original review text\" |\n"
    f"| 2     | awful         | 1.0        | bad              | 2.0           | \"original review text\" |\n\n"
    f"Column requirements:\n"
    f"- Index: Sequential numbering starting from 1\n"
    f"- Food Keywords: Display exactly ONE food-related keyword per review (no brackets)\n"
    f"- Food Score: Calculated numerical score (format: X.0, range 1.0-5.0) for food quality\n"
    f"- Service Keywords: Display exactly ONE service-related keyword per review (no brackets)\n"
    f"- Service Score: Calculated numerical score (format: X.0, range 1.0-5.0) for service quality\n"
    f"- Review Text: Complete original review text in double quotes\n\n"
    f"OUTPUT FORMAT:\n"
    f"First show your keyword extraction work, then provide the detailed analysis table, then provide:\n"
    f"food_scores=[score1, score2, ...]\n"
    f"customer_service_scores=[score1, score2, ...]\n\n"
    f"CRITICAL: Both score arrays must have exactly the same length (one score per review)."
)
SCORER = build_agent(
    "scoring_agent",
    "You receive restaurant name and two score lists (food_scores, customer_service_scores).\n"
    "Call calculate_overall_score with the restaurant name and both score lists.\n"
    "Use the exact restaurant name provided in the context."
)
ENTRY = build_agent("entry", "Coordinator")

# register functions
register_function(
    fetch_restaurant_data,
    caller=DATA_FETCH,
    executor=ENTRY,
    name="fetch_restaurant_data",
    description="Fetch reviews from specified data file by name.",
)
register_function(
    calculate_overall_score,
    caller=SCORER,
    executor=ENTRY,
    name="calculate_overall_score",
    description="Compute final rating via geometric mean.",
)


# ────────────────────────────────────────────────────────────────
# 3. Conversation helpers
# ────────────────────────────────────────────────────────────────

def run_chat_sequence(entry: ConversableAgent, sequence: list[dict]) -> str:
    ctx = {**getattr(entry, "_initiate_chats_ctx", {})}
    for step in sequence:
        msg = step["message"].format(**ctx)
        chat = entry.initiate_chat(
            step["recipient"], message=msg,
            summary_method=step.get("summary_method", "last_msg"),
            max_turns=step.get("max_turns", 2),
        )
        out = chat.summary
        # Data fetch output
        if step["recipient"] is DATA_FETCH:
            for past in reversed(chat.chat_history):
                try:
                    data = ast.literal_eval(past["content"])
                    if isinstance(data, dict) and data and not ("call" in data):
                        ctx.update({"reviews_dict": data, "restaurant_name": next(iter(data))})
                        break
                except:
                    continue
        # Analyzer output passed directly
        elif step["recipient"] is ANALYZER:
            ctx["analyzer_output"] = out
    return out

ConversableAgent.initiate_chats = lambda self, seq: run_chat_sequence(self, seq)

# ──────────────────────────────────────────────
# 4. Main entry
# ──────────────────────────────────────────────

def main(user_query: str, data_path: str = "restaurant-data.txt"):
    global DATA_PATH
    DATA_PATH = data_path
    agents = {"data_fetch": DATA_FETCH, "analyzer": ANALYZER, "scorer": SCORER}
    chat_sequence = [
        {"recipient": agents["data_fetch"],
         "message": "Find reviews for this query: {user_query}",
         "summary_method": "last_msg",
         "max_turns": 2},

        {"recipient": agents["analyzer"],
         "message": "Here are the reviews from the data fetch agent:\n{reviews_dict}\n\nExtract food and service scores for each review.",
         "summary_method": "last_msg",
         "max_turns": 1},

        {"recipient": agents["scorer"],
         "message": "Restaurant: {restaurant_name}\n{analyzer_output}",
         "summary_method": "last_msg",
         "max_turns": 2},
    ]
    ENTRY._initiate_chats_ctx = {"user_query": user_query}
    result = ENTRY.initiate_chats(chat_sequence)
    print(f"result: {result}")
    return result

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print('Usage: python main.py path/to/data.txt "How good is Subway?" ')
        sys.exit(1)

    path = sys.argv[1]
    query = sys.argv[2]
    main(query, path)
